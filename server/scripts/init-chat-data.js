const ChatGroup = require('../models/ChatGroup');
const { testConnection } = require('../config/db');

/**
 * 初始化聊天数据
 */
async function initChatData() {
  try {
    console.log('🔄 开始初始化聊天数据...');

    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败');
      process.exit(1);
    }

    // 创建默认群组
    const defaultGroups = [
      {
        group_name: '全国接单总群',
        group_avatar: null,
        description: '全国范围内的公司转让接单群，欢迎大家交流合作'
      },
      {
        group_name: '北京同行群',
        group_avatar: null,
        description: '北京地区同行交流群'
      },
      {
        group_name: '上海同行群',
        group_avatar: null,
        description: '上海地区同行交流群'
      },
      {
        group_name: '广州同行群',
        group_avatar: null,
        description: '广州地区同行交流群'
      },
      {
        group_name: '深圳同行群',
        group_avatar: null,
        description: '深圳地区同行交流群'
      }
    ];

    console.log('📝 创建默认群组...');
    for (const groupData of defaultGroups) {
      try {
        // 检查群组是否已存在
        const existingGroups = await ChatGroup.getList({ page: 1, pageSize: 100 });
        const exists = existingGroups.data.some(group => group.group_name === groupData.group_name);
        
        if (!exists) {
          const group = await ChatGroup.create(groupData);
          console.log(`✅ 创建群组: ${group.group_name} (ID: ${group.id})`);
        } else {
          console.log(`⚠️ 群组已存在: ${groupData.group_name}`);
        }
      } catch (error) {
        console.error(`❌ 创建群组失败: ${groupData.group_name}`, error.message);
      }
    }

    console.log('🎉 聊天数据初始化完成!');
    
  } catch (error) {
    console.error('❌ 初始化聊天数据失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initChatData().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { initChatData };
