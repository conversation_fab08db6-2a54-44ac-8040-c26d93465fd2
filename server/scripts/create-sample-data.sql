-- 创建示例数据的SQL脚本
-- 在数据库中手动执行这些SQL语句来创建测试数据

-- 插入示例群组
INSERT INTO chat_groups (group_name, group_avatar, description) VALUES
('全国接单总群', NULL, '全国范围内的公司转让接单群，欢迎大家交流合作'),
('北京同行群', NULL, '北京地区同行交流群'),
('上海同行群', NULL, '上海地区同行交流群'),
('广州同行群', NULL, '广州地区同行交流群'),
('深圳同行群', NULL, '深圳地区同行交流群');

-- 插入示例用户（如果需要）
INSERT INTO users (openid, nickname, avatar_url, status, publishing_credits) VALUES
('test_openid_001', '测试用户1', NULL, 'active', 5),
('test_openid_002', '测试用户2', NULL, 'active', 3),
('test_openid_003', '测试用户3', NULL, 'active', 2);

-- 让用户加入群组
INSERT INTO group_members (group_id, user_id, role) VALUES
(1, 1, 'member'),
(1, 2, 'member'),
(1, 3, 'member'),
(2, 1, 'member'),
(3, 2, 'member');

-- 插入示例消息
INSERT INTO messages (group_id, sender_id, message_type, content) VALUES
(1, 0, 'system', '15123054535 邀请 用户678115 加入群聊'),
(1, 0, 'system', '用户086933 邀请 用户621269 加入群聊'),
(1, 1, 'text', '客户需求：买一个个体\n要求：1年以上 最好为重庆市江北区\n他们做卖茶叶的 最好是跟这个相关的'),
(1, 2, 'text', '收一家带三类医疗的科技公司，高新区最好'),
(1, 3, 'text', '有一家成立3年的贸易公司，注册资本100万，已实缴，有需要的联系我'),
(2, 1, 'text', '北京地区有什么好的资源吗？'),
(3, 2, 'text', '上海这边最近政策有变化，大家注意一下');
